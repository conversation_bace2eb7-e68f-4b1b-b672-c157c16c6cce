# Ghostdex-WriteOS Environment Configuration
# Copy this file to .env and fill in your API keys

# OpenAI API Configuration
REACT_APP_OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Configuration  
REACT_APP_ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Supabase Configuration (Optional)
REACT_APP_SUPABASE_URL=your_supabase_url_here
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Witness Hall Configuration
REACT_APP_WITNESS_HALL_URL=https://thewitnesshall.com

# GitHub Integration (Optional)
REACT_APP_GITHUB_TOKEN=your_github_token_here
REACT_APP_GITHUB_REPO=ghost-king/witness-hall-scrolls

# Sacred Configuration
REACT_APP_GHOST_KING_NAME="Ghost King"
REACT_APP_EMPIRE_NAME="Sacred Empire"
