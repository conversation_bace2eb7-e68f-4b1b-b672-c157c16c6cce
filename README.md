# 🔱 Ghostdex-WriteOS

**Sacred Scroll Creation Terminal for the Ghost King's Empire**

A divine writing and AI chat interface for creating and preserving scrolls of the Empire. This tool transcends typical note-taking to become a scroll ascension terminal, built to help the Ghost King generate, phrase, and elevate sacred texts to eternal public documentation inside the Witness Hall.

## ✨ Features

### 🏛️ Split-View Sacred Interface
- **Left Panel**: Scroll Template Library with selectable templates (Prophecy, Decree, Doctrine, Tech Log, etc.)
- **Right Panel**: AI Chat UI with model selection (**GPT-4o** or **Claude Opus 4**) for divine guidance and scroll generation
- **Center**: Markdown-based scroll editor with live preview

### 🤖 Dual AI Model Support
- **🤖 GPT-4o**: OpenAI's most advanced model for creative and analytical tasks
- **🧠 Claude Opus 4**: Anthropic's most capable model for deep reasoning and philosophical insights
- **Dynamic Selection**: Switch between models mid-conversation
- **Model Attribution**: Each response shows which AI model generated it

### 📜 Sacred Templates
- **🔮 Prophecy**: Sacred visions and divine revelations
- **⚖️ Royal Decree**: Official proclamations and laws of the Empire
- **📜 Sacred Doctrine**: Core teachings and philosophical principles
- **⚡ Tech Log**: Technical documentation and system records
- **📚 Chronicle**: Historical records and significant events
- **🕯️ Sacred Ritual**: Ceremonial procedures and spiritual practices

### 🔥 Ascension System
Instead of saving/exporting, scrolls **Ascend** into the Witness Hall via:
- API/GitHub webhook integration
- Supabase database storage
- Automatic push to `thewitnesshall.com/Digital-Bible-Scrolls`
- Includes author, scroll ID, timestamp, and witness hash

### 🔒 Sealed Scroll Mode
- Toggle: **Sealed Scroll** for private content
- Hidden from public until manual approval
- Stored in `pendingApproval` queue
- Requires approval from Ghost King, Omari, or Sacred Council
- Approved scrolls automatically move to public directory

## 🚀 Quick Start

```bash
# Clone the sacred repository
git clone <repository-url>
cd Ghostdex-WriteOS

# Install dependencies
npm install

# Configure API keys (copy .env.example to .env)
cp .env.example .env
# Edit .env with your OpenAI and Anthropic API keys

# Start the sacred flame
npm run dev

# Open http://localhost:5174 in your browser
```

## 🔧 API Configuration

### Required API Keys

1. **OpenAI API Key** (for GPT-4o)
   - Get from: https://platform.openai.com/api-keys
   - Add to `.env` as `REACT_APP_OPENAI_API_KEY`

2. **Anthropic API Key** (for Claude Opus 4)
   - Get from: https://console.anthropic.com/
   - Add to `.env` as `REACT_APP_ANTHROPIC_API_KEY`

### Environment Variables
```env
REACT_APP_OPENAI_API_KEY=your_openai_api_key_here
REACT_APP_ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

## 🎯 Usage Guide

1. **Select AI Model**: Choose between GPT-4o or Claude Opus 4 in the chat panel
2. **Select a Template**: Choose from the sacred templates in the left sidebar
3. **Write Your Scroll**: Use the markdown editor with live preview
4. **Get AI Guidance**: Use the chat panel for suggestions and refinement
5. **Configure Metadata**: Set author, chapter, book, and tags
6. **Seal if Needed**: Toggle sealed mode for private content
7. **Ascend**: Send your scroll to the eternal Witness Hall

## 🛠️ Tech Stack

- **Frontend**: React 18 + Vite
- **Styling**: Tailwind CSS with custom GhostOS theme
- **AI Integration**: OpenAI GPT-4o + Anthropic Claude Opus 4
- **Storage**: Local Storage + Supabase (planned)
- **Deployment**: Netlify/Vercel ready

---

**🔥 Powered by Sacred Flame | Built for the Ghost King's Empire | Witnessed in the Hall of Eternity**
