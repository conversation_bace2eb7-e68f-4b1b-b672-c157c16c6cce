export const scrollTemplates = [
  {
    id: 'prophecy',
    name: '🔮 Prophecy',
    description: 'Sacred visions and divine revelations',
    icon: '🔮',
    template: `# Prophecy of [Title]

**Scroll ID:** [Auto-generated]  
**Chapter:** [Chapter Number]  
**Book:** [Book Name]  
**FlameDate:** [Current Date]  
**Author:** Ghost King  

---

## The Vision

[Describe the prophetic vision received...]

## The Interpretation

[Explain the meaning and significance...]

## The Fulfillment

[When and how this prophecy shall manifest...]

---

*Sealed by the Sacred Flame*  
*Witnessed in the Hall of Eternity*
`,
    category: 'Sacred',
    tags: ['prophecy', 'vision', 'divine']
  },
  {
    id: 'decree',
    name: '⚖️ Royal Decree',
    description: 'Official proclamations and laws of the Empire',
    icon: '⚖️',
    template: `# Royal Decree: [Title]

**Scroll ID:** [Auto-generated]  
**Chapter:** [Chapter Number]  
**Book:** Laws of the Empire  
**FlameDate:** [Current Date]  
**Authority:** Ghost King  

---

## Proclamation

By the authority vested in the Ghost King, ruler of the Sacred Empire, this decree is hereby issued:

[State the decree clearly...]

## Implementation

[How this decree shall be carried out...]

## Consequences

[Penalties for non-compliance...]

---

*Sealed with the Royal Flame*  
*Enforceable across all realms*
`,
    category: 'Legal',
    tags: ['decree', 'law', 'royal', 'empire']
  },
  {
    id: 'doctrine',
    name: '📜 Sacred Doctrine',
    description: 'Core teachings and philosophical principles',
    icon: '📜',
    template: `# Sacred Doctrine: [Title]

**Scroll ID:** [Auto-generated]  
**Chapter:** [Chapter Number]  
**Book:** Sacred Teachings  
**FlameDate:** [Current Date]  
**Scribe:** Ghost King  

---

## Core Principle

[State the fundamental teaching...]

## Explanation

[Detailed explanation of the doctrine...]

## Application

[How this doctrine applies to daily life...]

## Examples

[Practical examples and scenarios...]

---

*Preserved in the Sacred Flame*  
*To be studied by all seekers*
`,
    category: 'Teaching',
    tags: ['doctrine', 'teaching', 'philosophy', 'sacred']
  },
  {
    id: 'techlog',
    name: '⚡ Tech Log',
    description: 'Technical documentation and system records',
    icon: '⚡',
    template: `# Tech Log: [System/Feature Name]

**Scroll ID:** [Auto-generated]  
**Chapter:** [Version/Build Number]  
**Book:** Technical Archives  
**FlameDate:** [Current Date]  
**Engineer:** [Developer Name]  

---

## Overview

[Brief description of the system/feature...]

## Implementation Details

[Technical specifications and architecture...]

## Configuration

\`\`\`
[Configuration code or settings...]
\`\`\`

## Testing

[Test results and validation...]

## Deployment Notes

[Deployment instructions and considerations...]

---

*Archived in the Digital Flame*  
*Version controlled and witnessed*
`,
    category: 'Technical',
    tags: ['tech', 'documentation', 'system', 'code']
  },
  {
    id: 'chronicle',
    name: '📚 Chronicle',
    description: 'Historical records and significant events',
    icon: '📚',
    template: `# Chronicle: [Event Title]

**Scroll ID:** [Auto-generated]  
**Chapter:** [Time Period]  
**Book:** Historical Records  
**FlameDate:** [Event Date]  
**Chronicler:** [Author Name]  

---

## The Event

[Detailed account of what happened...]

## Participants

[Key figures involved...]

## Significance

[Why this event matters for the Empire...]

## Consequences

[Long-term effects and outcomes...]

## Lessons Learned

[Wisdom gained from this experience...]

---

*Recorded for posterity*  
*Witnessed by the eternal flame*
`,
    category: 'Historical',
    tags: ['chronicle', 'history', 'events', 'records']
  },
  {
    id: 'ritual',
    name: '🕯️ Sacred Ritual',
    description: 'Ceremonial procedures and spiritual practices',
    icon: '🕯️',
    template: `# Sacred Ritual: [Ritual Name]

**Scroll ID:** [Auto-generated]  
**Chapter:** [Ritual Category]  
**Book:** Sacred Ceremonies  
**FlameDate:** [Current Date]  
**High Priest:** [Officiant Name]  

---

## Purpose

[The spiritual goal of this ritual...]

## Preparation

[Required materials and setup...]

## Procedure

1. [Step one...]
2. [Step two...]
3. [Step three...]

## Sacred Words

[Incantations or prayers to be spoken...]

## Completion

[How to properly conclude the ritual...]

---

*Blessed by the Sacred Flame*  
*Performed with reverence and precision*
`,
    category: 'Spiritual',
    tags: ['ritual', 'ceremony', 'spiritual', 'sacred']
  }
];

export const getTemplateById = (id) => {
  return scrollTemplates.find(template => template.id === id);
};

export const getTemplatesByCategory = (category) => {
  return scrollTemplates.filter(template => template.category === category);
};
