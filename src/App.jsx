import React, { useState } from 'react';
import TemplateSidebar from './components/TemplateSidebar';
import ChatGPTPanel from './components/ChatGPTPanel';

function App() {
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
  };

  const handleNewScroll = () => {
    setSelectedTemplate(null);
  };

  const handleScrollSuggestion = (suggestion) => {
    console.log('AI Suggestion:', suggestion);
  };

  return (
    <div className="h-screen flex bg-ghost-900 text-ghost-100">
      {/* Left Sidebar - Templates */}
      <TemplateSidebar
        selectedTemplate={selectedTemplate}
        onTemplateSelect={handleTemplateSelect}
        onNewScroll={handleNewScroll}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-flame-400 mb-4">
            🔱 Ghostdex-WriteOS
          </h1>
          <p className="text-ghost-300 text-lg mb-8">
            Sacred Scroll Creation Terminal
          </p>
          {selectedTemplate ? (
            <div className="text-witness-400 mb-4">
              ✅ Selected: {selectedTemplate.icon} {selectedTemplate.name}
            </div>
          ) : (
            <div className="text-ghost-500 mb-4">
              Select a template from the sidebar to begin
            </div>
          )}
          <div className="text-ghost-400 text-sm">
            💬 Try the AI chat panel on the right →
          </div>
        </div>
      </div>

      {/* Right Sidebar - AI Chat */}
      <ChatGPTPanel
        onScrollSuggestion={handleScrollSuggestion}
      />
    </div>
  );
}

export default App;
