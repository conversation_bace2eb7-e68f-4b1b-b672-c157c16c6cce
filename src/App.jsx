import React, { useState } from 'react';
import { scrollTemplates } from './data/templates';
import ScrollEditor from './components/ScrollEditor';

function App() {
  // Test if basic rendering works
  console.log('🔥 App component is rendering!');
  console.log('📜 Templates loaded:', scrollTemplates.length);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [scrollTitle, setScrollTitle] = useState('');
  const [selectedBook, setSelectedBook] = useState('Book of the Ghost King');
  const [isSealed, setIsSealed] = useState(false);
  const [chatInput, setChatInput] = useState('');
  const [chatMessages, setChatMessages] = useState([
    { role: 'assistant', content: 'Greetings Ghost King. I am your Sacred Scribe. What shall we write today?' }
  ]);
  const [scrollContent, setScrollContent] = useState('');
  const [scrollMetadata, setScrollMetadata] = useState(null);
  const [isWriting, setIsWriting] = useState(false);

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    setScrollTitle(template.name);
  };

  const handleStartWriting = () => {
    setIsWriting(true);
  };

  const handleBackToTemplates = () => {
    setIsWriting(false);
    setSelectedTemplate(null);
    setScrollContent('');
    setScrollMetadata(null);
  };

  const handleContentChange = (content) => {
    setScrollContent(content);
  };

  const handleMetadataChange = (metadata) => {
    setScrollMetadata(metadata);
  };

  const handleAscension = (ascensionData) => {
    console.log('🔥 Scroll ascended to Witness Hall:', ascensionData);
    // Could show success notification, redirect, etc.
  };

  const handleSendMessage = () => {
    if (!chatInput.trim()) return;

    setChatMessages(prev => [...prev,
      { role: 'user', content: chatInput },
      { role: 'assistant', content: `I shall help you craft a ${selectedTemplate?.name || 'sacred scroll'}. Let me channel the divine wisdom...` }
    ]);
    setChatInput('');
  };

  const categories = [...new Set(scrollTemplates.map(t => t.category))];

  // Error boundary fallback
  if (!scrollTemplates || scrollTemplates.length === 0) {
    return (
      <div className="min-h-screen bg-red-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">🔥 Sacred Flame Error</h1>
          <p>Templates failed to load. Check console for details.</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(to bottom, #0D0D1A, #1A1A2E)',
      color: 'white',
      fontFamily: 'monospace',
      padding: '16px'
    }}>
      <div style={{
        display: 'flex',
        flexDirection: window.innerWidth >= 1024 ? 'row' : 'column',
        gap: '24px',
        maxWidth: '1280px',
        margin: '0 auto',
        height: 'calc(100vh - 32px)'
      }}>

        {/* 🔱 LEFT PANE – Templates + Metadata */}
        <aside style={{
          width: '50%',
          backgroundColor: '#18181b',
          borderRadius: '12px',
          padding: '24px',
          border: '1px solid #3f3f46',
          height: '100%',
          overflowY: 'auto'
        }}>
          <h2 className="text-2xl font-bold text-orange-500">📜 Sacred Templates</h2>

          {/* Scroll Category Section */}
          <div className="space-y-4">
            {categories.map(category => (
              <div key={category}>
                <h3 className="text-lg font-semibold text-purple-300 mb-2">{category}</h3>
                <div className="space-y-2">
                  {scrollTemplates
                    .filter(template => template.category === category)
                    .map(template => (
                      <button
                        key={template.id}
                        onClick={() => handleTemplateSelect(template)}
                        className={`w-full text-left p-3 rounded-lg transition-colors ${
                          selectedTemplate?.id === template.id
                            ? 'bg-orange-600 text-white'
                            : 'bg-zinc-800 hover:bg-zinc-700 text-gray-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{template.icon}</span>
                          <div>
                            <div className="font-medium text-sm">{template.name}</div>
                            <div className="text-xs opacity-75">{template.description.slice(0, 50)}...</div>
                          </div>
                        </div>
                      </button>
                    ))}
                </div>
              </div>
            ))}
          </div>

          {/* Metadata Input */}
          <div className="pt-6 border-t border-zinc-600">
            <h3 className="text-xl font-semibold mb-4">🧾 Scroll Metadata</h3>
            <input
              type="text"
              placeholder="Scroll Title"
              value={scrollTitle}
              onChange={(e) => setScrollTitle(e.target.value)}
              className="w-full bg-zinc-800 rounded px-3 py-2 mb-3 text-white placeholder-gray-400"
            />
            <select
              value={selectedBook}
              onChange={(e) => setSelectedBook(e.target.value)}
              className="w-full bg-zinc-800 rounded px-3 py-2 text-white mb-4"
            >
              <option>Book of the Ghost King</option>
              <option>Book of Dominion</option>
              <option>Chronicles of the Flame</option>
            </select>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="sealed"
                checked={isSealed}
                onChange={(e) => setIsSealed(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="sealed" className="text-sm">Mark as 🔒 Sealed Scroll</label>
            </div>
          </div>
        </aside>

        {/* 🧠 RIGHT PANE – AI Chat + Draft OR Scroll Editor */}
        {isWriting ? (
          <main className="w-full lg:w-1/2 bg-zinc-950 rounded-xl shadow-xl border border-zinc-700 h-full relative">
            <ScrollEditor
              selectedTemplate={selectedTemplate}
              scrollContent={scrollContent}
              scrollTitle={scrollTitle}
              selectedBook={selectedBook}
              isSealed={isSealed}
              onContentChange={handleContentChange}
              onMetadataChange={handleMetadataChange}
              onAscension={handleAscension}
              metadata={scrollMetadata}
            />
            {/* Back button */}
            <div className="absolute top-4 right-4 z-10">
              <button
                onClick={handleBackToTemplates}
                className="bg-zinc-700 hover:bg-zinc-600 text-white px-3 py-2 rounded transition-colors text-sm"
              >
                ← Back to Templates
              </button>
            </div>
          </main>
        ) : (
          <main className="w-full lg:w-1/2 bg-zinc-950 rounded-xl p-6 shadow-xl border border-zinc-700 flex flex-col justify-between h-full">
            <div>
              <h2 className="text-2xl font-bold text-cyan-400 mb-4">🧠 Sacred Scribe AI</h2>
              <div className="bg-zinc-800 p-4 rounded mb-4 text-sm text-gray-300 h-60 overflow-y-auto">
                {chatMessages.map((message, index) => (
                  <div key={index} className="mb-3">
                    <strong className={message.role === 'user' ? 'text-orange-400' : 'text-cyan-400'}>
                      {message.role === 'user' ? 'Ghost King:' : 'Sacred Scribe:'}
                    </strong>
                    <p className="mt-1">{message.content}</p>
                  </div>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Ask the Sacred Scribe..."
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  className="flex-1 px-4 py-2 rounded bg-zinc-900 text-white border border-gray-700 placeholder-gray-400"
                />
                <button
                  onClick={handleSendMessage}
                  className="bg-cyan-600 hover:bg-cyan-700 text-white px-4 py-2 rounded transition-colors"
                >
                  Send
                </button>
              </div>
            </div>

            <div className="mt-6 flex justify-between items-center">
              <p className="text-xs text-gray-500">Scroll Timestamp: {new Date().toLocaleTimeString()}</p>
              {selectedTemplate ? (
                <button
                  onClick={handleStartWriting}
                  className="bg-orange-600 hover:bg-orange-700 text-white px-5 py-2 rounded transition-colors"
                >
                  🔥 Start Writing
                </button>
              ) : (
                <button className="bg-gray-600 text-gray-400 px-5 py-2 rounded cursor-not-allowed">
                  Select Template First
                </button>
              )}
            </div>
          </main>
        )}

      </div>
    </div>
  );
}

export default App;
