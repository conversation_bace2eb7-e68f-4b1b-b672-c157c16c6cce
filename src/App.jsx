import React, { useState } from 'react';
import TemplateSidebar from './components/TemplateSidebar';
import ChatGPTPanel from './components/ChatGPTPanel';

function App() {
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
  };

  const handleNewScroll = () => {
    setSelectedTemplate(null);
  };

  const handleScrollSuggestion = (suggestion) => {
    console.log('AI Suggestion:', suggestion);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 px-8 py-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-orange-400 mb-2">
            🔱 Ghostdex-WriteOS
          </h1>
          <p className="text-gray-300">
            Sacred Scroll Creation Terminal for the Ghost King's Empire
          </p>
        </div>
      </header>

      {/* Main Layout */}
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

          {/* Left Panel - Templates */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-xl border border-gray-700 h-fit">
              <TemplateSidebar
                selectedTemplate={selectedTemplate}
                onTemplateSelect={handleTemplateSelect}
                onNewScroll={handleNewScroll}
              />
            </div>
          </div>

          {/* Center Panel - Main Content */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-xl border border-gray-700 p-8 text-center min-h-[600px] flex flex-col justify-center">
              {selectedTemplate ? (
                <div>
                  <div className="text-6xl mb-6">{selectedTemplate.icon}</div>
                  <h2 className="text-2xl font-bold text-orange-400 mb-4">
                    {selectedTemplate.name}
                  </h2>
                  <p className="text-gray-300 mb-8 leading-relaxed">
                    {selectedTemplate.description}
                  </p>
                  <button className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    🔥 Start Writing
                  </button>
                </div>
              ) : (
                <div>
                  <div className="text-6xl mb-6">📜</div>
                  <h2 className="text-2xl font-bold text-gray-400 mb-4">
                    Choose Your Sacred Template
                  </h2>
                  <p className="text-gray-500 leading-relaxed">
                    Select a template from the left panel to begin crafting your scroll
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - AI Chat */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-xl border border-gray-700 h-fit">
              <ChatGPTPanel
                onScrollSuggestion={handleScrollSuggestion}
              />
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}

export default App;
