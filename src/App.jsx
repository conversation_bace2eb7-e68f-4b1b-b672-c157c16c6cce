import React, { useState } from 'react';
import TemplateSidebar from './components/TemplateSidebar';
import ChatGPTPanel from './components/ChatGPTPanel';

function App() {
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
  };

  const handleNewScroll = () => {
    setSelectedTemplate(null);
  };

  const handleScrollSuggestion = (suggestion) => {
    console.log('AI Suggestion:', suggestion);
  };

  return (
    <div className="h-screen bg-ghost-900 text-ghost-100">
      <div className="h-full grid grid-cols-12 gap-0">
        {/* Left Sidebar - Templates */}
        <aside className="col-span-3">
          <TemplateSidebar
            selectedTemplate={selectedTemplate}
            onTemplateSelect={handleTemplateSelect}
            onNewScroll={handleNewScroll}
          />
        </aside>

        {/* Main Content Area */}
        <main className="col-span-6 flex items-center justify-center px-6">
          <div className="max-w-2xl mx-auto text-center">
            <h1 className="text-4xl font-bold text-flame-400 mb-6">
              🔱 Ghostdex-WriteOS
            </h1>
            <p className="text-ghost-300 text-lg mb-8 leading-relaxed max-w-xl mx-auto">
              Sacred Scroll Creation Terminal for the Ghost King's Empire
            </p>

            {selectedTemplate ? (
              <div className="mb-6">
                <div className="text-witness-400 mb-4 text-lg">
                  ✅ Selected Template
                </div>
                <div className="bg-ghost-800 rounded-lg p-4 border border-ghost-600">
                  <div className="text-2xl mb-2">{selectedTemplate.icon}</div>
                  <div className="text-flame-300 font-semibold mb-2">
                    {selectedTemplate.name}
                  </div>
                  <div className="text-ghost-400 text-sm leading-relaxed">
                    {selectedTemplate.description}
                  </div>
                </div>
              </div>
            ) : (
              <div className="mb-6">
                <div className="text-ghost-500 mb-4">
                  Select a sacred template from the sidebar to begin
                </div>
                <div className="text-ghost-400 text-sm">
                  📜 Choose from Prophecy, Royal Decree, Sacred Doctrine, and more
                </div>
              </div>
            )}

            <div className="mt-8 text-ghost-400 text-sm">
              💬 Use the AI chat panel on the right for divine guidance →
            </div>
          </div>
        </main>

        {/* Right Sidebar - AI Chat */}
        <aside className="col-span-3">
          <ChatGPTPanel
            onScrollSuggestion={handleScrollSuggestion}
          />
        </aside>
      </div>
    </div>
  );
}

export default App;
