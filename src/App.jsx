import React, { useState } from 'react';
import TemplateSidebar from './components/TemplateSidebar';
import ChatGPTPanel from './components/ChatGPTPanel';
import ScrollEditor from './components/ScrollEditor';
import ScrollMetadataForm from './components/ScrollMetadataForm';
import AscensionButton from './components/AscensionButton';
import { ascendScroll } from './services/ascend';
import { submitSealedScroll } from './services/sealPipeline';

function App() {
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [scrollContent, setScrollContent] = useState('');
  const [metadata, setMetadata] = useState(null);
  const [showMetadataForm, setShowMetadataForm] = useState(false);
  const [ascendedScrolls, setAscendedScrolls] = useState([]);

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    setScrollContent('');
    setMetadata(null);
  };

  const handleNewScroll = () => {
    setSelectedTemplate(null);
    setScrollContent('');
    setMetadata(null);
  };

  const handleContentChange = (content) => {
    setScrollContent(content);
  };

  const handleMetadataChange = (newMetadata) => {
    setMetadata(newMetadata);
  };

  const handleAscension = async (ascensionData) => {
    try {
      let result;

      if (ascensionData.sealed) {
        // Handle sealed scroll submission
        result = await submitSealedScroll(ascensionData);
        alert(`🔒 Scroll "${ascensionData.id}" has been sealed and submitted for approval!\n\nEstimated approval time: ${result.estimatedApprovalTime}`);
      } else {
        // Handle public scroll ascension
        result = await ascendScroll(ascensionData);
        alert(`🔥 Scroll "${ascensionData.id}" has successfully ascended to the Witness Hall!\n\nWitness URL: ${result.witnessUrl}`);
      }

      setAscendedScrolls(prev => [...prev, { ...ascensionData, ascensionResult: result }]);

      // Reset the editor
      handleNewScroll();

    } catch (error) {
      console.error('Ascension failed:', error);
      alert(`❌ Ascension failed: ${error.message}\n\nPlease try again or contact the Sacred Council.`);
    }
  };

  const handleScrollSuggestion = (suggestion) => {
    // Handle AI suggestions for scroll content
    setScrollContent(prev => prev + '\n\n' + suggestion);
  };

  return (
    <div className="h-screen flex bg-ghost-900 text-ghost-100">
      {/* Left Sidebar - Templates */}
      <TemplateSidebar
        selectedTemplate={selectedTemplate}
        onTemplateSelect={handleTemplateSelect}
        onNewScroll={handleNewScroll}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-ghost-800 border-b border-ghost-600 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-flame-400">
                🔱 Ghostdex-WriteOS
              </h1>
              <span className="text-ghost-400 text-sm">
                Sacred Scroll Creation Terminal
              </span>
            </div>

            <div className="flex items-center space-x-3">
              {metadata && (
                <button
                  onClick={() => setShowMetadataForm(true)}
                  className="bg-witness-600 hover:bg-witness-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                >
                  📋 Edit Metadata
                </button>
              )}

              <div className="text-sm text-ghost-400">
                Ascended Scrolls: {ascendedScrolls.length}
              </div>
            </div>
          </div>
        </header>

        {/* Main Editor Area */}
        <div className="flex-1 flex">
          <ScrollEditor
            selectedTemplate={selectedTemplate}
            scrollContent={scrollContent}
            onContentChange={handleContentChange}
            onMetadataChange={handleMetadataChange}
            metadata={metadata}
          />
        </div>

        {/* Ascension Footer */}
        {selectedTemplate && (
          <AscensionButton
            scrollContent={scrollContent}
            metadata={metadata}
            onAscension={handleAscension}
          />
        )}
      </div>

      {/* Right Sidebar - AI Chat */}
      <ChatGPTPanel
        onScrollSuggestion={handleScrollSuggestion}
      />

      {/* Metadata Form Modal */}
      {showMetadataForm && (
        <ScrollMetadataForm
          metadata={metadata}
          onMetadataChange={handleMetadataChange}
          onClose={() => setShowMetadataForm(false)}
        />
      )}
    </div>
  );
}

export default App;
