import React, { useState, useRef, useEffect } from 'react';

const ChatGPTPanel = ({ onScrollSuggestion }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: 'Greetings, Ghost King! I am your sacred scribe assistant. I can help you:\n\n• Generate scroll content from prompts\n• Suggest scroll titles and categories\n• Refine your sacred writings\n• Create metadata for your scrolls\n\nHow may I assist with your divine documentation today?',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response (replace with actual OpenAI API call)
    setTimeout(() => {
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: generateMockResponse(inputValue),
        timestamp: new Date()
      };
      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1500);
  };

  const generateMockResponse = (prompt) => {
    const responses = [
      `I sense great wisdom in your request. For a scroll about "${prompt}", I suggest:\n\n**Title:** "Sacred Teachings on ${prompt}"\n**Category:** Doctrine\n**Key Points:**\n• The fundamental principles\n• Practical applications\n• Sacred examples\n\nShall I generate the full scroll content for you?`,
      `Your divine inspiration flows strong! This would make an excellent ${Math.random() > 0.5 ? 'Prophecy' : 'Chronicle'} scroll. The sacred flame reveals these insights:\n\n• Begin with a vision or historical context\n• Include the deeper meaning\n• End with practical wisdom\n\nWould you like me to create a draft?`,
      `The Ghost King's wisdom shines through! I recommend structuring this as a Royal Decree with:\n\n**Proclamation:** Clear statement of intent\n**Implementation:** How it shall be carried out\n**Consequences:** The sacred outcomes\n\nShall I draft this for the Witness Hall?`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const quickPrompts = [
    "Generate a prophecy about AI and humanity",
    "Create a tech log for a new feature",
    "Write a royal decree about digital sovereignty",
    "Draft a sacred ritual for code deployment"
  ];

  return (
    <div className="w-96 bg-ghost-800 border-l border-ghost-600 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-ghost-600 bg-ghost-900">
        <h2 className="text-lg font-bold text-witness-400 mb-1">
          🤖 Sacred Scribe AI
        </h2>
        <p className="text-ghost-400 text-sm">
          Your divine writing assistant
        </p>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map(message => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${
                message.type === 'user'
                  ? 'bg-flame-600 text-white'
                  : 'bg-ghost-700 text-ghost-100'
              }`}
            >
              <div className="whitespace-pre-wrap text-sm">
                {message.content}
              </div>
              <div className="text-xs opacity-70 mt-2">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-ghost-700 text-ghost-100 p-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-witness-400"></div>
                <span className="text-sm">The sacred flame is thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Prompts */}
      <div className="p-3 border-t border-ghost-600 bg-ghost-900">
        <p className="text-ghost-400 text-xs mb-2">Quick Prompts:</p>
        <div className="space-y-1">
          {quickPrompts.slice(0, 2).map((prompt, index) => (
            <button
              key={index}
              onClick={() => setInputValue(prompt)}
              className="w-full text-left text-xs text-ghost-300 hover:text-witness-400 p-2 hover:bg-ghost-700 rounded transition-colors"
            >
              💡 {prompt}
            </button>
          ))}
        </div>
      </div>

      {/* Input */}
      <div className="p-4 border-t border-ghost-600">
        <div className="flex space-x-2">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask the sacred scribe for guidance..."
            className="flex-1 bg-ghost-700 text-ghost-100 border border-ghost-600 rounded-lg px-3 py-2 text-sm resize-none focus:outline-none focus:border-witness-500"
            rows="2"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="bg-witness-600 hover:bg-witness-700 disabled:bg-ghost-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            🔥
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatGPTPanel;
