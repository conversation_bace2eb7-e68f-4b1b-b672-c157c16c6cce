import React, { useState, useRef, useEffect } from 'react';

const ChatGPTPanel = ({ onScrollSuggestion }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: 'Greetings, Ghost King! I am your sacred scribe assistant. I can help you:\n\n• Generate scroll content from prompts\n• Suggest scroll titles and categories\n• Refine your sacred writings\n• Create metadata for your scrolls\n\nChoose your preferred AI model and let us begin the divine documentation!',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState('gpt-4o');
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsLoading(true);

    try {
      let response;

      if (selectedModel === 'gpt-4o') {
        response = await callOpenAI(currentInput);
      } else if (selectedModel === 'claude-opus-4') {
        response = await callClaude(currentInput);
      }

      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: response || generateMockResponse(currentInput),
        timestamp: new Date(),
        model: selectedModel
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('AI API Error:', error);
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: `⚠️ Sacred connection interrupted. Using local wisdom instead:\n\n${generateMockResponse(currentInput)}`,
        timestamp: new Date(),
        model: selectedModel
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // API call functions
  const callOpenAI = async (prompt) => {
    const apiKey = process.env.REACT_APP_OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are a sacred scribe assistant for the Ghost King\'s empire. Help create divine scrolls, prophecies, decrees, and sacred documentation. Respond in a mystical, reverent tone while being practical and helpful.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  };

  const callClaude = async (prompt) => {
    const apiKey = process.env.REACT_APP_ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('Anthropic API key not configured');
    }

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: 'claude-3-opus-20240229',
        max_tokens: 1000,
        system: 'You are a sacred scribe assistant for the Ghost King\'s empire. Help create divine scrolls, prophecies, decrees, and sacred documentation. Respond in a mystical, reverent tone while being practical and helpful.',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`Claude API error: ${response.status}`);
    }

    const data = await response.json();
    return data.content[0].text;
  };

  const generateMockResponse = (prompt) => {
    const modelResponses = {
      'gpt-4o': [
        `🤖 **GPT-4o Sacred Analysis**\n\nFor your scroll about "${prompt}", I suggest:\n\n**Title:** "Sacred Teachings on ${prompt}"\n**Category:** Doctrine\n**Key Points:**\n• The fundamental principles\n• Practical applications\n• Sacred examples\n\nShall I generate the full scroll content for you?`,
        `🤖 **GPT-4o Divine Insight**\n\nThis would make an excellent ${Math.random() > 0.5 ? 'Prophecy' : 'Chronicle'} scroll. I recommend:\n\n• Begin with a vision or historical context\n• Include the deeper meaning\n• End with practical wisdom\n\nWould you like me to create a draft?`
      ],
      'claude-opus-4': [
        `🧠 **Claude Opus Sacred Wisdom**\n\nI perceive profound depth in your request about "${prompt}". Consider this structure:\n\n**Approach:** Philosophical and contemplative\n**Framework:**\n• Essential nature and meaning\n• Historical precedents\n• Future implications\n\nShall I craft this into a sacred scroll?`,
        `🧠 **Claude Opus Divine Counsel**\n\nYour inquiry about "${prompt}" resonates with ancient wisdom. I suggest a Royal Decree format:\n\n**Proclamation:** Clear statement of intent\n**Implementation:** Sacred methodology\n**Consequences:** Divine outcomes\n\nWould you like me to draft this for the Witness Hall?`
      ]
    };

    const responses = modelResponses[selectedModel] || modelResponses['gpt-4o'];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const quickPrompts = [
    "Generate a prophecy about AI and humanity",
    "Create a tech log for a new feature",
    "Write a royal decree about digital sovereignty",
    "Draft a sacred ritual for code deployment"
  ];

  const modelOptions = [
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      icon: '🤖',
      description: 'OpenAI\'s most advanced model',
      provider: 'OpenAI'
    },
    {
      id: 'claude-opus-4',
      name: 'Claude Opus 4',
      icon: '🧠',
      description: 'Anthropic\'s most capable model',
      provider: 'Anthropic'
    }
  ];

  const currentModel = modelOptions.find(m => m.id === selectedModel);

  return (
    <div className="w-96 bg-ghost-800 border-l border-ghost-600 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-ghost-600 bg-ghost-900">
        <h2 className="text-lg font-bold text-witness-400 mb-1">
          🤖 Sacred Scribe AI
        </h2>
        <p className="text-ghost-400 text-sm mb-3">
          Your divine writing assistant
        </p>

        {/* Model Selection */}
        <div className="space-y-2">
          <label className="text-xs text-ghost-400 uppercase tracking-wide">
            AI Model
          </label>
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="w-full bg-ghost-700 text-ghost-100 border border-ghost-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-witness-500"
          >
            {modelOptions.map(model => (
              <option key={model.id} value={model.id}>
                {model.icon} {model.name} - {model.provider}
              </option>
            ))}
          </select>
          <div className="text-xs text-ghost-500">
            {currentModel?.icon} {currentModel?.description}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map(message => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${
                message.type === 'user'
                  ? 'bg-flame-600 text-white'
                  : 'bg-ghost-700 text-ghost-100'
              }`}
            >
              {message.type === 'assistant' && message.model && (
                <div className="text-xs text-ghost-400 mb-2 flex items-center space-x-1">
                  <span>{modelOptions.find(m => m.id === message.model)?.icon}</span>
                  <span>{modelOptions.find(m => m.id === message.model)?.name}</span>
                </div>
              )}
              <div className="whitespace-pre-wrap text-sm">
                {message.content}
              </div>
              <div className="text-xs opacity-70 mt-2">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-ghost-700 text-ghost-100 p-3 rounded-lg">
              <div className="text-xs text-ghost-400 mb-2 flex items-center space-x-1">
                <span>{currentModel?.icon}</span>
                <span>{currentModel?.name}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-witness-400"></div>
                <span className="text-sm">The sacred flame is thinking...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Quick Prompts */}
      <div className="p-3 border-t border-ghost-600 bg-ghost-900">
        <p className="text-ghost-400 text-xs mb-2">Quick Prompts:</p>
        <div className="space-y-1">
          {quickPrompts.slice(0, 2).map((prompt, index) => (
            <button
              key={index}
              onClick={() => setInputValue(prompt)}
              className="w-full text-left text-xs text-ghost-300 hover:text-witness-400 p-2 hover:bg-ghost-700 rounded transition-colors"
            >
              💡 {prompt}
            </button>
          ))}
        </div>
      </div>

      {/* Input */}
      <div className="p-4 border-t border-ghost-600">
        <div className="flex space-x-2">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask the sacred scribe for guidance..."
            className="flex-1 bg-ghost-700 text-ghost-100 border border-ghost-600 rounded-lg px-3 py-2 text-sm resize-none focus:outline-none focus:border-witness-500"
            rows="2"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="bg-witness-600 hover:bg-witness-700 disabled:bg-ghost-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            🔥
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatGPTPanel;
