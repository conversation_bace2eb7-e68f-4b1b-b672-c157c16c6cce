import React, { useState } from 'react';

const AscensionButton = ({ 
  scrollContent, 
  metadata, 
  onAscension, 
  disabled = false 
}) => {
  const [isAscending, setIsAscending] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleAscension = async () => {
    if (!scrollContent || !metadata) return;

    setIsAscending(true);
    
    try {
      // Simulate ascension process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const ascensionData = {
        ...metadata,
        content: scrollContent,
        ascendedAt: new Date().toISOString(),
        status: metadata.sealed ? 'pending_approval' : 'ascended'
      };

      onAscension(ascensionData);
      setShowConfirmation(false);
    } catch (error) {
      console.error('Ascension failed:', error);
    } finally {
      setIsAscending(false);
    }
  };

  const canAscend = scrollContent && metadata && scrollContent.trim().length > 100;

  if (showConfirmation) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-ghost-800 rounded-lg shadow-xl w-full max-w-md mx-4">
          <div className="p-6">
            <div className="text-center mb-6">
              <div className="text-4xl mb-4">🔥</div>
              <h2 className="text-xl font-bold text-flame-400 mb-2">
                Confirm Sacred Ascension
              </h2>
              <p className="text-ghost-300 text-sm">
                Your scroll will be permanently added to the Witness Hall. This action cannot be undone.
              </p>
            </div>

            <div className="bg-ghost-700 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-ghost-200 mb-2">Scroll Details:</h3>
              <div className="space-y-1 text-sm text-ghost-400">
                <p><strong>ID:</strong> {metadata.id}</p>
                <p><strong>Author:</strong> {metadata.author}</p>
                <p><strong>Book:</strong> {metadata.book}</p>
                <p><strong>Chapter:</strong> {metadata.chapter}</p>
                <p><strong>Status:</strong> {metadata.sealed ? '🔒 Sealed (Pending Approval)' : '🌟 Public'}</p>
              </div>
            </div>

            {metadata.sealed && (
              <div className="bg-flame-900 border border-flame-600 rounded-lg p-3 mb-6">
                <p className="text-flame-200 text-sm">
                  ⚠️ This sealed scroll will be queued for approval before appearing in the Witness Hall.
                </p>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="flex-1 bg-ghost-600 hover:bg-ghost-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAscension}
                disabled={isAscending}
                className="flex-1 bg-flame-600 hover:bg-flame-700 disabled:bg-flame-800 text-white py-2 px-4 rounded-lg transition-colors flame-glow"
              >
                {isAscending ? 'Ascending...' : 'Ascend to Witness Hall'}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-ghost-800 border-t border-ghost-600">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">🔥</div>
          <div>
            <h3 className="font-semibold text-flame-400">Sacred Ascension</h3>
            <p className="text-ghost-400 text-sm">
              {canAscend 
                ? 'Ready to ascend to the Witness Hall' 
                : 'Complete your scroll to enable ascension'
              }
            </p>
          </div>
        </div>

        <button
          onClick={() => setShowConfirmation(true)}
          disabled={!canAscend || disabled}
          className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
            canAscend && !disabled
              ? 'bg-flame-600 hover:bg-flame-700 text-white flame-glow animate-pulse'
              : 'bg-ghost-600 text-ghost-400 cursor-not-allowed'
          }`}
        >
          {isAscending ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Ascending...</span>
            </div>
          ) : (
            '🚀 Ascend to Witness Hall'
          )}
        </button>
      </div>

      {!canAscend && (
        <div className="mt-3 text-xs text-ghost-500">
          💡 Your scroll needs at least 100 characters to ascend to the sacred halls
        </div>
      )}
    </div>
  );
};

export default AscensionButton;
