import React, { useState, useEffect } from 'react';
import { generateScrollMetadata } from '../utils/generateScrollID';
import AscensionButton from './AscensionButton';

const ScrollEditor = ({
  selectedTemplate,
  scrollContent,
  scrollTitle,
  selectedBook,
  isSealed,
  onContentChange,
  onMetadataChange,
  onAscension,
  metadata
}) => {
  const [content, setContent] = useState('');
  const [isPreview, setIsPreview] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);

  useEffect(() => {
    if (selectedTemplate && !scrollContent) {
      const newMetadata = generateScrollMetadata(selectedTemplate.id);
      let templateContent = selectedTemplate.template;

      // Replace placeholders with actual values
      templateContent = templateContent
        .replace(/\[Auto-generated\]/g, newMetadata.id)
        .replace(/\[Current Date\]/g, newMetadata.flameDate)
        .replace(/\[Chapter Number\]/g, newMetadata.chapter)
        .replace(/\[Book Name\]/g, newMetadata.book);

      setContent(templateContent);
      onContentChange(templateContent);
      onMetadataChange(newMetadata);
    } else if (scrollContent) {
      setContent(scrollContent);
    }
  }, [selectedTemplate, scrollContent]);

  const handleContentChange = (newContent) => {
    setContent(newContent);
    onContentChange(newContent);

    // Auto-save simulation
    setLastSaved(new Date());
  };

  const renderMarkdown = (text) => {
    // Simple markdown rendering for preview
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-flame-400 mb-4">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-witness-400 mb-3">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-medium text-ghost-200 mb-2">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-flame-300">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic text-ghost-300">$1</em>')
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-ghost-900 p-4 rounded-lg overflow-x-auto"><code class="text-witness-300">$1</code></pre>')
      .replace(/`(.*?)`/g, '<code class="bg-ghost-700 px-2 py-1 rounded text-witness-400">$1</code>')
      .replace(/^---$/gim, '<hr class="border-ghost-600 my-6">')
      .replace(/\n/g, '<br>');
  };

  if (!selectedTemplate) {
    return (
      <div className="flex-1 flex items-center justify-center bg-ghost-900">
        <div className="text-center">
          <div className="text-6xl mb-4">📜</div>
          <h2 className="text-xl font-semibold text-ghost-300 mb-2">
            Select a Sacred Template
          </h2>
          <p className="text-ghost-500">
            Choose a template from the sidebar to begin crafting your scroll
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-ghost-900">
      {/* Editor Header */}
      <div className="bg-ghost-800 border-b border-ghost-600 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold text-ghost-100">
              {selectedTemplate.icon} {selectedTemplate.name}
            </h1>
            <span className="text-sm text-ghost-400">
              {metadata?.id || 'New Scroll'}
            </span>
          </div>

          <div className="flex items-center space-x-3">
            {lastSaved && (
              <span className="text-xs text-ghost-500">
                Saved {lastSaved.toLocaleTimeString()}
              </span>
            )}

            <div className="flex bg-ghost-700 rounded-lg p-1">
              <button
                onClick={() => setIsPreview(false)}
                className={`px-3 py-1 text-sm rounded transition-colors ${
                  !isPreview
                    ? 'bg-flame-600 text-white'
                    : 'text-ghost-300 hover:text-white'
                }`}
              >
                ✏️ Edit
              </button>
              <button
                onClick={() => setIsPreview(true)}
                className={`px-3 py-1 text-sm rounded transition-colors ${
                  isPreview
                    ? 'bg-flame-600 text-white'
                    : 'text-ghost-300 hover:text-white'
                }`}
              >
                👁️ Preview
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 overflow-hidden">
        {isPreview ? (
          <div className="h-full overflow-y-auto p-8">
            <div
              className="max-w-4xl mx-auto prose prose-invert scroll-content"
              dangerouslySetInnerHTML={{
                __html: renderMarkdown(content)
              }}
            />
          </div>
        ) : (
          <div className="h-full flex">
            <textarea
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              className="flex-1 bg-ghost-900 text-ghost-100 p-8 resize-none focus:outline-none font-mono text-sm leading-relaxed"
              placeholder="Begin writing your sacred scroll..."
              style={{ fontFamily: 'Georgia, serif' }}
            />
          </div>
        )}
      </div>

      {/* Editor Footer */}
      <div className="bg-zinc-800 border-t border-zinc-600 p-4">
        <div className="flex items-center justify-between text-sm mb-4">
          <div className="flex items-center space-x-4 text-gray-400">
            <span>Words: {content.split(/\s+/).filter(word => word.length > 0).length}</span>
            <span>Characters: {content.length}</span>
            <span>Lines: {content.split('\n').length}</span>
          </div>

          <div className="flex items-center space-x-2 text-gray-500">
            <span>🔥</span>
            <span>Sacred Flame Active</span>
          </div>
        </div>

        {/* Ascension Button */}
        <AscensionButton
          scrollContent={content}
          scrollTitle={scrollTitle}
          selectedTemplate={selectedTemplate}
          selectedBook={selectedBook}
          metadata={metadata}
          isSealed={isSealed}
          onAscension={onAscension}
        />
      </div>
    </div>
  );
};

export default ScrollEditor;
