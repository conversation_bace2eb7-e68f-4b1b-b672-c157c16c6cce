import React from 'react';
import { scrollTemplates } from '../data/templates.js';

const TemplateSidebar = ({ selectedTemplate, onTemplateSelect, onNewScroll }) => {
  const categories = [...new Set(scrollTemplates.map(t => t.category))];

  return (
    <div className="w-80 bg-ghost-800 border-r border-ghost-600 flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-ghost-600">
        <h2 className="text-xl font-bold text-flame-400 mb-2">
          🔱 Sacred Templates
        </h2>
        <p className="text-ghost-300 text-sm">
          Choose a template to begin your scroll
        </p>
      </div>

      {/* New Scroll Button */}
      <div className="p-4 border-b border-ghost-600">
        <button
          onClick={onNewScroll}
          className="w-full bg-flame-600 hover:bg-flame-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flame-glow"
        >
          ✨ Create New Scroll
        </button>
      </div>

      {/* Templates by Category */}
      <div className="flex-1 overflow-y-auto">
        {categories.map(category => (
          <div key={category} className="mb-6">
            <div className="px-4 py-2 bg-ghost-700">
              <h3 className="text-witness-400 font-semibold text-sm uppercase tracking-wide">
                {category}
              </h3>
            </div>
            <div className="space-y-1">
              {scrollTemplates
                .filter(template => template.category === category)
                .map(template => (
                  <button
                    key={template.id}
                    onClick={() => onTemplateSelect(template)}
                    className={`w-full text-left p-4 hover:bg-ghost-700 transition-colors duration-200 border-l-4 ${
                      selectedTemplate?.id === template.id
                        ? 'border-flame-500 bg-ghost-700'
                        : 'border-transparent'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">{template.icon}</span>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-ghost-100 font-medium text-sm mb-1">
                          {template.name}
                        </h4>
                        <p className="text-ghost-400 text-xs leading-relaxed">
                          {template.description}
                        </p>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {template.tags.slice(0, 2).map(tag => (
                            <span
                              key={tag}
                              className="inline-block bg-ghost-600 text-ghost-300 text-xs px-2 py-1 rounded"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-ghost-600 bg-ghost-900">
        <div className="text-center">
          <p className="text-ghost-400 text-xs mb-1">
            🔥 Powered by Sacred Flame
          </p>
          <p className="text-ghost-500 text-xs">
            Ghostdex-WriteOS v1.0
          </p>
        </div>
      </div>
    </div>
  );
};

export default TemplateSidebar;
