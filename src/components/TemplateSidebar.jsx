import React from 'react';
import { scrollTemplates } from '../data/templates';

const TemplateSidebar = ({ selectedTemplate, onTemplateSelect, onNewScroll }) => {
  const categories = [...new Set(scrollTemplates.map(t => t.category))];

  return (
    <div className="flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h2 className="text-xl font-bold text-orange-400 mb-3">
          📜 Sacred Templates
        </h2>
        <p className="text-gray-300 text-sm">
          Choose a template to begin
        </p>
      </div>

      {/* New Scroll Button */}
      <div className="p-4 border-b border-gray-700">
        <button
          onClick={onNewScroll}
          className="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          ✨ New Scroll
        </button>
      </div>

      {/* Templates by Category */}
      <div className="max-h-96 overflow-y-auto">
        {categories.map(category => (
          <div key={category} className="p-4">
            <h3 className="text-sm font-semibold text-blue-400 mb-3 uppercase tracking-wide">
              {category}
            </h3>
            <div className="space-y-2">
              {scrollTemplates
                .filter(template => template.category === category)
                .map(template => (
                  <button
                    key={template.id}
                    onClick={() => onTemplateSelect(template)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedTemplate?.id === template.id
                        ? 'bg-orange-600 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-200'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{template.icon}</span>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">
                          {template.name}
                        </div>
                        <div className="text-xs opacity-75 truncate">
                          {template.description.slice(0, 40)}...
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-ghost-600 bg-ghost-900">
        <div className="text-center">
          <p className="text-ghost-400 text-xs mb-1">
            🔥 Powered by Sacred Flame
          </p>
          <p className="text-ghost-500 text-xs">
            Ghostdex-WriteOS v1.0
          </p>
        </div>
      </div>
    </div>
  );
};

export default TemplateSidebar;
