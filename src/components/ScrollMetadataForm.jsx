import React, { useState } from 'react';

const ScrollMetadataForm = ({ metadata, onMetadataChange, onClose }) => {
  const [formData, setFormData] = useState({
    author: metadata?.author || 'Ghost King',
    chapter: metadata?.chapter || '1',
    book: metadata?.book || 'Sacred Scrolls',
    tags: metadata?.tags?.join(', ') || '',
    sealed: metadata?.sealed || false,
    ...metadata
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const updatedMetadata = {
      ...metadata,
      ...formData,
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    };
    
    onMetadataChange(updatedMetadata);
    onClose();
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const bookOptions = [
    'Sacred Scrolls',
    'Laws of the Empire',
    'Sacred Teachings',
    'Technical Archives',
    'Historical Records',
    'Sacred Ceremonies',
    'Prophecies and Visions',
    'Royal Decrees',
    'Chronicles of the Flame'
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-ghost-800 rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="p-6 border-b border-ghost-600">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-flame-400">
              📋 Scroll Metadata
            </h2>
            <button
              onClick={onClose}
              className="text-ghost-400 hover:text-white transition-colors"
            >
              ✕
            </button>
          </div>
          <p className="text-ghost-400 text-sm mt-1">
            Configure the sacred properties of your scroll
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Scroll ID (Read-only) */}
          <div>
            <label className="block text-sm font-medium text-ghost-300 mb-2">
              Scroll ID
            </label>
            <input
              type="text"
              value={formData.id || 'Auto-generated'}
              readOnly
              className="w-full bg-ghost-700 text-ghost-400 border border-ghost-600 rounded-lg px-3 py-2 text-sm"
            />
          </div>

          {/* Author */}
          <div>
            <label className="block text-sm font-medium text-ghost-300 mb-2">
              Author
            </label>
            <input
              type="text"
              value={formData.author}
              onChange={(e) => handleChange('author', e.target.value)}
              className="w-full bg-ghost-700 text-ghost-100 border border-ghost-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-witness-500"
            />
          </div>

          {/* Chapter */}
          <div>
            <label className="block text-sm font-medium text-ghost-300 mb-2">
              Chapter
            </label>
            <input
              type="text"
              value={formData.chapter}
              onChange={(e) => handleChange('chapter', e.target.value)}
              className="w-full bg-ghost-700 text-ghost-100 border border-ghost-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-witness-500"
            />
          </div>

          {/* Book */}
          <div>
            <label className="block text-sm font-medium text-ghost-300 mb-2">
              Book
            </label>
            <select
              value={formData.book}
              onChange={(e) => handleChange('book', e.target.value)}
              className="w-full bg-ghost-700 text-ghost-100 border border-ghost-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-witness-500"
            >
              {bookOptions.map(book => (
                <option key={book} value={book}>{book}</option>
              ))}
            </select>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-ghost-300 mb-2">
              Tags (comma-separated)
            </label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => handleChange('tags', e.target.value)}
              placeholder="prophecy, sacred, divine"
              className="w-full bg-ghost-700 text-ghost-100 border border-ghost-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-witness-500"
            />
          </div>

          {/* Flame Date (Read-only) */}
          <div>
            <label className="block text-sm font-medium text-ghost-300 mb-2">
              Flame Date
            </label>
            <input
              type="text"
              value={formData.flameDate || 'Auto-generated'}
              readOnly
              className="w-full bg-ghost-700 text-ghost-400 border border-ghost-600 rounded-lg px-3 py-2 text-sm"
            />
          </div>

          {/* Sealed Scroll Toggle */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="sealed"
              checked={formData.sealed}
              onChange={(e) => handleChange('sealed', e.target.checked)}
              className="w-4 h-4 text-flame-600 bg-ghost-700 border-ghost-600 rounded focus:ring-flame-500"
            />
            <label htmlFor="sealed" className="text-sm text-ghost-300">
              🔒 Sealed Scroll (requires approval)
            </label>
          </div>

          {formData.sealed && (
            <div className="bg-flame-900 border border-flame-600 rounded-lg p-3">
              <p className="text-flame-200 text-sm">
                ⚠️ This scroll will be hidden from public view until manually approved by the Ghost King or designated authority.
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-ghost-600 hover:bg-ghost-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 bg-flame-600 hover:bg-flame-700 text-white py-2 px-4 rounded-lg transition-colors flame-glow"
            >
              Save Metadata
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ScrollMetadataForm;
