/**
 * Sealed Scroll Pipeline - Manages approval workflow for sealed scrolls
 * This service handles the approval process for scrolls marked as sealed
 */

const SEAL_STORAGE_KEY = 'ghostdex_sealed_scrolls';
const APPROVAL_AUTHORITIES = ['Ghost King', '<PERSON><PERSON>', 'Sacred Council'];

/**
 * Submit a scroll to the sealed approval pipeline
 * @param {Object} scrollData - The sealed scroll data
 * @returns {Promise<Object>} - Submission result
 */
export const submitSealedScroll = async (scrollData) => {
  try {
    console.log('🔒 Submitting scroll to sealed pipeline:', scrollData.id);
    
    const sealedScroll = {
      ...scrollData,
      sealedAt: new Date().toISOString(),
      status: 'pending_approval',
      approvalLevel: 'level_1', // level_1, level_2, level_3
      reviewers: [],
      approvalHistory: [],
      priority: determinePriority(scrollData)
    };
    
    // Store in sealed pipeline
    await storeSealedScroll(sealedScroll);
    
    // Notify approval authorities (simulate)
    await notifyApprovalAuthorities(sealedScroll);
    
    return {
      success: true,
      sealId: sealedScroll.id,
      status: 'sealed_pending',
      estimatedApprovalTime: getEstimatedApprovalTime(sealedScroll.priority),
      message: 'Scroll sealed and submitted for approval'
    };
    
  } catch (error) {
    console.error('Failed to submit sealed scroll:', error);
    throw new Error(`Seal submission failed: ${error.message}`);
  }
};

/**
 * Get all sealed scrolls pending approval
 */
export const getPendingSealedScrolls = () => {
  try {
    const sealedScrolls = JSON.parse(
      localStorage.getItem(SEAL_STORAGE_KEY) || '[]'
    );
    
    return sealedScrolls.filter(scroll => 
      scroll.status === 'pending_approval'
    );
  } catch (error) {
    console.error('Failed to retrieve sealed scrolls:', error);
    return [];
  }
};

/**
 * Approve a sealed scroll
 * @param {string} scrollId - The scroll ID to approve
 * @param {string} approver - The authority approving the scroll
 * @param {string} comments - Optional approval comments
 */
export const approveSealedScroll = async (scrollId, approver, comments = '') => {
  try {
    const sealedScrolls = JSON.parse(
      localStorage.getItem(SEAL_STORAGE_KEY) || '[]'
    );
    
    const scrollIndex = sealedScrolls.findIndex(s => s.id === scrollId);
    if (scrollIndex === -1) {
      throw new Error('Sealed scroll not found');
    }
    
    const scroll = sealedScrolls[scrollIndex];
    
    // Validate approver authority
    if (!APPROVAL_AUTHORITIES.includes(approver)) {
      throw new Error('Insufficient authority to approve sealed scrolls');
    }
    
    // Add approval record
    const approvalRecord = {
      approver,
      approvedAt: new Date().toISOString(),
      comments,
      level: scroll.approvalLevel
    };
    
    scroll.approvalHistory.push(approvalRecord);
    scroll.reviewers.push(approver);
    scroll.status = 'approved';
    scroll.approvedAt = new Date().toISOString();
    scroll.approvedBy = approver;
    
    // Update storage
    sealedScrolls[scrollIndex] = scroll;
    localStorage.setItem(SEAL_STORAGE_KEY, JSON.stringify(sealedScrolls));
    
    console.log(`✅ Scroll ${scrollId} approved by ${approver}`);
    
    return {
      success: true,
      scrollId,
      approver,
      status: 'approved',
      message: 'Sealed scroll approved and ready for ascension'
    };
    
  } catch (error) {
    console.error('Failed to approve sealed scroll:', error);
    throw error;
  }
};

/**
 * Reject a sealed scroll
 * @param {string} scrollId - The scroll ID to reject
 * @param {string} reviewer - The authority rejecting the scroll
 * @param {string} reason - Reason for rejection
 */
export const rejectSealedScroll = async (scrollId, reviewer, reason) => {
  try {
    const sealedScrolls = JSON.parse(
      localStorage.getItem(SEAL_STORAGE_KEY) || '[]'
    );
    
    const scrollIndex = sealedScrolls.findIndex(s => s.id === scrollId);
    if (scrollIndex === -1) {
      throw new Error('Sealed scroll not found');
    }
    
    const scroll = sealedScrolls[scrollIndex];
    
    // Add rejection record
    const rejectionRecord = {
      reviewer,
      rejectedAt: new Date().toISOString(),
      reason,
      level: scroll.approvalLevel
    };
    
    scroll.approvalHistory.push(rejectionRecord);
    scroll.status = 'rejected';
    scroll.rejectedAt = new Date().toISOString();
    scroll.rejectedBy = reviewer;
    scroll.rejectionReason = reason;
    
    // Update storage
    sealedScrolls[scrollIndex] = scroll;
    localStorage.setItem(SEAL_STORAGE_KEY, JSON.stringify(sealedScrolls));
    
    console.log(`❌ Scroll ${scrollId} rejected by ${reviewer}: ${reason}`);
    
    return {
      success: true,
      scrollId,
      reviewer,
      status: 'rejected',
      reason,
      message: 'Sealed scroll rejected'
    };
    
  } catch (error) {
    console.error('Failed to reject sealed scroll:', error);
    throw error;
  }
};

/**
 * Get approval statistics
 */
export const getApprovalStats = () => {
  try {
    const sealedScrolls = JSON.parse(
      localStorage.getItem(SEAL_STORAGE_KEY) || '[]'
    );
    
    const stats = {
      total: sealedScrolls.length,
      pending: sealedScrolls.filter(s => s.status === 'pending_approval').length,
      approved: sealedScrolls.filter(s => s.status === 'approved').length,
      rejected: sealedScrolls.filter(s => s.status === 'rejected').length,
      averageApprovalTime: calculateAverageApprovalTime(sealedScrolls),
      topApprovers: getTopApprovers(sealedScrolls)
    };
    
    return stats;
  } catch (error) {
    console.error('Failed to calculate approval stats:', error);
    return {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      averageApprovalTime: 0,
      topApprovers: []
    };
  }
};

// Helper functions

const storeSealedScroll = async (scrollData) => {
  const sealedScrolls = JSON.parse(
    localStorage.getItem(SEAL_STORAGE_KEY) || '[]'
  );
  
  sealedScrolls.push(scrollData);
  localStorage.setItem(SEAL_STORAGE_KEY, JSON.stringify(sealedScrolls));
};

const determinePriority = (scrollData) => {
  // Determine priority based on content type and author
  if (scrollData.templateId === 'decree') return 'high';
  if (scrollData.templateId === 'prophecy') return 'high';
  if (scrollData.author === 'Ghost King') return 'high';
  if (scrollData.templateId === 'techlog') return 'medium';
  return 'normal';
};

const getEstimatedApprovalTime = (priority) => {
  const times = {
    high: '1-2 hours',
    medium: '4-8 hours',
    normal: '1-2 days'
  };
  return times[priority] || '1-2 days';
};

const notifyApprovalAuthorities = async (scrollData) => {
  // In a real implementation, this would send notifications
  console.log(`📧 Notifying approval authorities about sealed scroll: ${scrollData.id}`);
  
  // Simulate notification delay
  await new Promise(resolve => setTimeout(resolve, 500));
};

const calculateAverageApprovalTime = (scrolls) => {
  const approvedScrolls = scrolls.filter(s => s.status === 'approved' && s.sealedAt && s.approvedAt);
  
  if (approvedScrolls.length === 0) return 0;
  
  const totalTime = approvedScrolls.reduce((sum, scroll) => {
    const sealedTime = new Date(scroll.sealedAt);
    const approvedTime = new Date(scroll.approvedAt);
    return sum + (approvedTime - sealedTime);
  }, 0);
  
  return Math.round(totalTime / approvedScrolls.length / (1000 * 60 * 60)); // Hours
};

const getTopApprovers = (scrolls) => {
  const approverCounts = {};
  
  scrolls.forEach(scroll => {
    if (scroll.approvedBy) {
      approverCounts[scroll.approvedBy] = (approverCounts[scroll.approvedBy] || 0) + 1;
    }
  });
  
  return Object.entries(approverCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([approver, count]) => ({ approver, count }));
};
