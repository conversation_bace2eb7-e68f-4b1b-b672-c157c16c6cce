@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f1f5f9;
}

/* Sacred scroll styling */
.scroll-content {
  font-family: 'Georgia', serif;
  line-height: 1.8;
}

/* Flame animation for sacred elements */
.flame-glow {
  box-shadow: 0 0 20px rgba(242, 117, 10, 0.3);
  transition: all 0.3s ease;
}

.flame-glow:hover {
  box-shadow: 0 0 30px rgba(242, 117, 10, 0.5);
}

/* Custom scrollbar for the sacred interface */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}
