/**
 * Generate a unique scroll ID for the Ghostdex-WriteOS system
 * Format: GHOST-[YYYY]-[MM]-[DD]-[HHMMSS]-[RANDOM]
 */
export const generateScrollID = () => {
  const now = new Date();
  
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  // Generate a random 4-character suffix
  const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
  
  return `GHOST-${year}-${month}-${day}-${hours}${minutes}${seconds}-${randomSuffix}`;
};

/**
 * Generate a FlameDate in the sacred format
 * Format: "Flame Year [YYYY], Moon [MM], Day [DD]"
 */
export const generateFlameDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  
  const moonNames = [
    'Ember', 'Frost', 'Storm', 'Bloom', 'Light', 'Fire',
    'Thunder', 'Harvest', 'Shadow', 'Mist', 'Crystal', 'Void'
  ];
  
  const moonName = moonNames[month - 1];
  
  return `Flame Year ${year}, Moon of ${moonName}, Day ${day}`;
};

/**
 * Generate metadata for a new scroll
 */
export const generateScrollMetadata = (templateId, customData = {}) => {
  return {
    id: generateScrollID(),
    flameDate: generateFlameDate(),
    created: new Date().toISOString(),
    templateId,
    author: customData.author || 'Ghost King',
    chapter: customData.chapter || '1',
    book: customData.book || 'Sacred Scrolls',
    tags: customData.tags || [],
    sealed: customData.sealed || false,
    status: 'draft'
  };
};

/**
 * Validate scroll ID format
 */
export const isValidScrollID = (scrollId) => {
  const pattern = /^GHOST-\d{4}-\d{2}-\d{2}-\d{6}-[A-Z0-9]{4}$/;
  return pattern.test(scrollId);
};
